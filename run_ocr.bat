@echo off
chcp 65001 >nul
title OCR图片处理器

echo.
echo ╔══════════════════════════════════════════════════════════════╗
echo ║                     通用OCR处理器 v1.0                        ║
echo ║                                                              ║
echo ║  功能: 批量处理图片，提取文本信息                              ║
echo ║  支持: 车牌识别、车型识别、接单次数、手机号码等                  ║
echo ║  输出: JSON、CSV、汇总报告                                    ║
echo ╚══════════════════════════════════════════════════════════════╝
echo.

REM 检查Python是否存在
python --version >nul 2>&1
if errorlevel 1 (
    echo 错误: 未找到Python，请确保Python已安装并添加到PATH环境变量
    pause
    exit /b 1
)

REM 检查必要文件是否存在
if not exist "config.py" (
    echo 错误: 未找到config.py配置文件
    pause
    exit /b 1
)

if not exist "run_ocr.py" (
    echo 错误: 未找到run_ocr.py启动脚本
    pause
    exit /b 1
)

if not exist "data" (
    echo 错误: 未找到data数据目录
    echo 请确保data目录存在并包含要处理的图片文件
    pause
    exit /b 1
)

echo 正在启动OCR处理器...
echo.

REM 运行Python脚本
python run_ocr.py

REM 检查执行结果
if errorlevel 1 (
    echo.
    echo 程序执行出现错误，请检查上方的错误信息
) else (
    echo.
    echo 程序执行完成！
    echo 请查看output目录中的结果文件
)

echo.
pause
