#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
配置文件 - 集中管理所有路径和参数配置
修改此文件中的配置即可适应不同的环境
"""

import os
from pathlib import Path


class Config:
    """配置类 - 包含所有可配置的参数"""
    
    # ==================== 路径配置 ====================
    
    # Python解释器路径 - 根据您的环境修改
    PYTHON_PATH = r"D:\ProgramData\miniconda3\envs\basePyEnv\python.exe"
    
    # 数据目录路径 - 包含要处理的图片的目录
    DATA_DIR = "data"
    
    # 输出目录路径 - 处理结果保存的目录
    OUTPUT_DIR = "output"
    
    # 临时文件目录
    TEMP_DIR = "temp"
    
    # 日志文件目录
    LOG_DIR = "logs"
    
    # ==================== 文件格式配置 ====================
    
    # 支持的图片格式
    SUPPORTED_IMAGE_EXTENSIONS = {'.jpg', '.jpeg', '.png', '.bmp', '.tiff', '.webp'}
    
    # 输出文件名模板（支持时间戳格式化）
    OUTPUT_JSON_TEMPLATE = "ocr_results_{timestamp}.json"
    OUTPUT_CSV_TEMPLATE = "ocr_results_{timestamp}.csv"
    OUTPUT_SUMMARY_TEMPLATE = "ocr_summary_{timestamp}.txt"
    OUTPUT_LOG_TEMPLATE = "ocr_log_{timestamp}.log"
    
    # ==================== OCR配置 ====================
    
    # PaddleOCR语言设置
    OCR_LANGUAGE = 'ch'  # 中文
    
    # 是否使用GPU加速 (需要安装CUDNN)
    USE_GPU = False  # 暂时禁用，需要安装CUDNN后才能启用
    
    # 是否显示OCR详细日志
    SHOW_OCR_LOG = False
    
    # OCR置信度阈值
    MIN_CONFIDENCE = 0.5
    
    # 文本检测阈值
    TEXT_DET_THRESH = 0.3
    
    # 文本框选阈值
    TEXT_DET_BOX_THRESH = 0.5
    
    # ==================== 识别模式配置 ====================

    # 是否启用区域裁剪模式（针对固定格式的截图）
    ENABLE_CROP_MODE = False

    # 裁剪区域配置（仅在ENABLE_CROP_MODE=True时生效）
    CROP_REGIONS = [
        (50, 1800, 1000, 1950, "车牌区域"),      # (x1, y1, x2, y2, 区域名称)
        (50, 1950, 1000, 2100, "车型区域"),
        (600, 1950, 1050, 2100, "接单次数区域")
    ]

    # ==================== 图片预处理配置 ====================

    # 是否启用图片裁剪（截掉上半部分）
    ENABLE_IMAGE_CROP = True

    # 裁剪比例（保留下半部分的比例，0.5表示保留下半50%）
    CROP_RATIO = 0.5

    # 裁剪模式：'bottom' - 保留下半部分，'top' - 保留上半部分
    CROP_MODE = 'bottom'
    
    # ==================== 正则表达式配置 ====================
    
    # 车牌号码正则表达式
    LICENSE_PLATE_PATTERNS = [
        r'[京津沪渝冀豫云辽黑湘皖鲁新苏浙赣鄂桂甘晋蒙陕吉闽贵粤青藏川宁琼使领][A-Z][A-Z0-9]{5}',
        r'[京津沪渝冀豫云辽黑湘皖鲁新苏浙赣鄂桂甘晋蒙陕吉闽贵粤青藏川宁琼使领][A-Z][A-Z0-9]{4}[A-Z0-9挂学警港澳]',
    ]
    
    # 颜色关键词
    COLOR_KEYWORDS = ['蓝', '黄', '白', '黑', '绿', '红', '灰', '银', '棕', '紫']
    
    # 车型关键词
    VEHICLE_KEYWORDS = [
        '轿车', 'SUV', 'MPV', '面包车', '货车', '客车', '卡车', '皮卡',
        '奔驰', '宝马', '奥迪', '大众', '丰田', '本田', '日产', '马自达',
        '比亚迪', '吉利', '长安', '哈弗', '红旗', '蔚来', '理想', '小鹏',
        '特斯拉', '广汽', '埃安', '零跑', '深蓝', '乐道', '问界', '极氪',
        '岚图', '高合', '威马', '小米', '华为', '极星', '路特斯', '哪吒',
        '荣威', '五菱', '雪佛兰', '别克', '凯迪拉克', '福特', '林肯',
        '启辰', '东风', '一汽', '北京汽车', 'ARCFOX', '极狐', '昊铂'
    ]
    
    # 接单次数正则表达式
    ORDER_COUNT_PATTERNS = [
        r'(\d+)\s*次',
        r'(\d+)\s*单',
        r'接单\s*(\d+)',
        r'(\d+)\s*接单',
        r'完成\s*(\d+)',
        r'(\d+)\s*完成'
    ]
    
    # 手机号码正则表达式
    PHONE_NUMBER_PATTERN = r'1[3-9]\d{9}'
    
    # ==================== 输出格式配置 ====================

    # 只生成CSV文件
    GENERATE_CSV_ONLY = True

    # CSV文件编码
    CSV_ENCODING = 'utf-8-sig'  # 支持Excel打开中文

    # JSON文件编码
    JSON_ENCODING = 'utf-8'

    # 是否在JSON中使用ASCII编码
    JSON_ENSURE_ASCII = False

    # JSON缩进
    JSON_INDENT = 2
    
    # ==================== 处理选项配置 ====================
    
    # 是否递归处理子目录
    RECURSIVE_PROCESSING = True
    
    # 是否跳过已处理的文件（基于文件修改时间）
    SKIP_PROCESSED_FILES = False
    
    # 处理失败时是否继续
    CONTINUE_ON_ERROR = True
    
    # 是否保存处理失败的文件列表
    SAVE_ERROR_LIST = True
    
    # 是否显示处理进度
    SHOW_PROGRESS = True
    
    # ==================== 性能配置 ====================
    
    # 批处理大小
    BATCH_SIZE = 1
    
    # 最大并发数
    MAX_WORKERS = 2
    
    # 内存限制（MB）
    MEMORY_LIMIT = 1024
    
    # ==================== 方法 ====================
    
    @classmethod
    def get_absolute_path(cls, relative_path: str) -> Path:
        """获取相对于项目根目录的绝对路径"""
        return Path(__file__).parent / relative_path
    
    @classmethod
    def ensure_dir_exists(cls, dir_path: str) -> Path:
        """确保目录存在，如果不存在则创建"""
        path = cls.get_absolute_path(dir_path)
        path.mkdir(parents=True, exist_ok=True)
        return path
    
    @classmethod
    def get_data_path(cls) -> Path:
        """获取数据目录路径"""
        return cls.get_absolute_path(cls.DATA_DIR)
    
    @classmethod
    def get_output_path(cls) -> Path:
        """获取输出目录路径"""
        return cls.ensure_dir_exists(cls.OUTPUT_DIR)
    
    @classmethod
    def get_temp_path(cls) -> Path:
        """获取临时目录路径"""
        return cls.ensure_dir_exists(cls.TEMP_DIR)
    
    @classmethod
    def get_log_path(cls) -> Path:
        """获取日志目录路径"""
        return cls.ensure_dir_exists(cls.LOG_DIR)
    
    @classmethod
    def validate_config(cls) -> bool:
        """验证配置是否有效"""
        errors = []
        
        # 检查Python路径
        if not Path(cls.PYTHON_PATH).exists():
            errors.append(f"Python路径不存在: {cls.PYTHON_PATH}")
        
        # 检查数据目录
        if not cls.get_data_path().exists():
            errors.append(f"数据目录不存在: {cls.DATA_DIR}")
        
        # 检查置信度阈值
        if not 0 <= cls.MIN_CONFIDENCE <= 1:
            errors.append(f"置信度阈值应在0-1之间: {cls.MIN_CONFIDENCE}")
        
        if errors:
            print("配置验证失败:")
            for error in errors:
                print(f"  - {error}")
            return False
        
        return True
    
    @classmethod
    def print_config(cls):
        """打印当前配置"""
        print("当前配置:")
        print(f"  Python路径: {cls.PYTHON_PATH}")
        print(f"  数据目录: {cls.DATA_DIR}")
        print(f"  输出目录: {cls.OUTPUT_DIR}")
        print(f"  OCR语言: {cls.OCR_LANGUAGE}")
        print(f"  使用GPU: {cls.USE_GPU}")
        print(f"  最小置信度: {cls.MIN_CONFIDENCE}")
        print(f"  区域裁剪模式: {cls.ENABLE_CROP_MODE}")
        print(f"  递归处理: {cls.RECURSIVE_PROCESSING}")


# 创建全局配置实例
config = Config()


if __name__ == "__main__":
    # 测试配置
    config.print_config()
    print()
    
    if config.validate_config():
        print("配置验证通过！")
    else:
        print("配置验证失败！")
