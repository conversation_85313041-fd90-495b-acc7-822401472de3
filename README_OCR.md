# 通用OCR处理器

一个基于PaddleOCR的通用图片文本识别工具，专门用于批量处理data目录下的所有图片文件，提取文本信息并生成结构化的结果。

## 功能特点

- 🔍 **智能文本识别**: 基于PaddleOCR，支持中文文本识别
- 🚗 **专业信息提取**: 自动识别车牌号码、车型、接单次数、手机号码等
- 📁 **批量处理**: 递归处理data目录下的所有图片文件
- 📊 **多格式输出**: 支持JSON、CSV、汇总报告等多种输出格式
- ⚙️ **灵活配置**: 集中的配置管理，便于修改路径和参数
- 📈 **进度显示**: 实时显示处理进度和统计信息
- 🔧 **易于维护**: 模块化设计，路径配置集中管理

## 项目结构

```
py_ocr/
├── data/                           # 数据目录（包含要处理的图片）
│   ├── 广州/
│   ├── 杭州/
│   ├── 深圳/
│   └── 郑州/
├── output/                         # 输出目录（自动创建）
├── logs/                          # 日志目录（自动创建）
├── config.py                      # 配置文件（重要：路径配置）
├── universal_ocr_processor.py     # 核心OCR处理器
├── main_ocr_processor.py          # 主程序
├── run_ocr.py                     # 启动脚本
├── requirements.txt               # 依赖包列表
└── README_OCR.md                  # 使用说明
```

## 安装依赖

1. 确保Python环境已安装（推荐Python 3.8+）
2. 安装依赖包：

```bash
pip install -r requirements.txt
```

或手动安装：

```bash
pip install paddlepaddle>=2.4.0
pip install paddleocr>=2.6.0
pip install opencv-python>=4.5.0
pip install Pillow>=8.0.0
pip install numpy>=1.19.0
```

## 配置说明

### 修改路径配置

编辑 `config.py` 文件中的路径配置：

```python
class Config:
    # Python解释器路径 - 根据您的环境修改
    PYTHON_PATH = r"D:\ProgramData\miniconda3\envs\basePyEnv\python.exe"
    
    # 数据目录路径 - 包含要处理的图片的目录
    DATA_DIR = "data"
    
    # 输出目录路径 - 处理结果保存的目录
    OUTPUT_DIR = "output"
```

### 其他重要配置

```python
# OCR配置
OCR_LANGUAGE = 'ch'          # 语言设置
USE_GPU = False              # 是否使用GPU
MIN_CONFIDENCE = 0.5         # 最小置信度阈值

# 处理选项
RECURSIVE_PROCESSING = True   # 是否递归处理子目录
CONTINUE_ON_ERROR = True     # 处理失败时是否继续
SHOW_PROGRESS = True         # 是否显示进度
```

## 使用方法

### 1. 基本使用

```bash
# 使用默认配置处理data目录
python run_ocr.py
```

### 2. 指定数据目录

```bash
# 处理指定目录
python run_ocr.py --data-dir "my_images"
```

### 3. 指定输出目录

```bash
# 指定输出目录
python run_ocr.py --output-dir "results"
```

### 4. 指定Python路径

```bash
# 指定Python解释器路径
python run_ocr.py --python-path "C:\Python39\python.exe"
```

### 5. 查看配置

```bash
# 显示当前配置
python run_ocr.py --config
```

### 6. 其他选项

```bash
# 禁用GPU加速
python run_ocr.py --no-gpu

# 设置最小置信度
python run_ocr.py --min-confidence 0.7

# 静默模式
python run_ocr.py --quiet
```

## 输出文件说明

处理完成后，会在输出目录生成以下文件：

### 1. JSON结果文件
- 文件名: `ocr_results_YYYYMMDD_HHMMSS.json`
- 内容: 完整的OCR识别结果，包含所有文本信息和分析结果

### 2. CSV结果文件
- 文件名: `ocr_results_YYYYMMDD_HHMMSS.csv`
- 内容: 表格格式的结果，便于Excel打开查看
- 列包括: 图片路径、文件名、城市、人员、平台、车牌号码、车型等

### 3. 汇总报告
- 文件名: `ocr_summary_YYYYMMDD_HHMMSS.txt`
- 内容: 处理结果的统计汇总，包含总体统计和详细结果

### 4. 日志文件
- 文件名: `ocr_log_YYYYMMDD_HHMMSS.log`
- 内容: 详细的处理日志，包含错误信息和调试信息

## 识别功能

### 支持的信息类型

1. **车牌号码**: 自动识别中国车牌号码格式
2. **车牌颜色**: 识别蓝、黄、白、黑、绿、红等颜色
3. **车型信息**: 识别轿车、SUV、MPV等车型和品牌
4. **接单次数**: 识别包含"次"、"单"等关键词的数字
5. **手机号码**: 识别11位手机号码格式
6. **其他文本**: 保存所有识别到的文本信息

### 路径信息解析

程序会自动从文件路径中提取信息：
- 路径格式: `data/城市/类别/人员/平台/文件名`
- 例如: `data/广州/双边渗透率/唐玉凤/哈啰/screenshot.jpg`

## 常见问题

### 1. 依赖安装问题

如果PaddleOCR安装失败，可以尝试：
```bash
pip install paddlepaddle -i https://mirror.baidu.com/pypi/simple
pip install paddleocr -i https://mirror.baidu.com/pypi/simple
```

### 2. 路径问题

- 确保 `config.py` 中的 `PYTHON_PATH` 指向正确的Python解释器
- 确保 `DATA_DIR` 指向包含图片的目录
- 路径中包含中文时，使用原始字符串（r"路径"）

### 3. 内存不足

如果处理大量图片时内存不足：
- 在 `config.py` 中设置 `BATCH_SIZE = 1`
- 设置 `MEMORY_LIMIT` 限制内存使用

### 4. 识别效果不佳

- 调整 `MIN_CONFIDENCE` 阈值
- 检查图片质量和清晰度
- 考虑使用GPU加速（设置 `USE_GPU = True`）

## 自定义扩展

### 添加新的识别模式

在 `universal_ocr_processor.py` 的 `analyze_text_content` 方法中添加新的正则表达式和关键词。

### 修改输出格式

在 `save_results_csv` 方法中修改CSV列定义，或在 `save_summary_report` 中修改报告格式。

### 添加新的配置项

在 `config.py` 中添加新的配置项，并在相应的处理器中使用。

## 技术支持

如有问题或建议，请检查：
1. 日志文件中的错误信息
2. 配置文件是否正确
3. 依赖包是否完整安装
4. 数据目录是否存在且包含图片文件

## 更新日志

- v1.0: 初始版本，支持基本的OCR识别和批量处理功能
