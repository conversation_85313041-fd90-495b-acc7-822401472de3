#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
通用OCR处理器
支持处理data目录下的所有图片文件，提取文本信息
路径配置集中管理，便于替换
"""

import os
import re
import json
import csv
from pathlib import Path
from typing import Dict, List, Tuple, Optional, Union
import cv2
import numpy as np
from paddleocr import PaddleOCR
from datetime import datetime


class PathConfig:
    """路径配置类 - 集中管理所有路径相关配置"""
    
    def __init__(self, python_path: str = None, data_dir: str = None, output_dir: str = None):
        # Python解释器路径
        self.python_path = python_path or r"D:\ProgramData\miniconda3\envs\basePyEnv\python.exe"
        
        # 数据目录路径
        self.data_dir = data_dir or "data"
        
        # 输出目录路径
        self.output_dir = output_dir or "output"
        
        # 支持的图片格式
        self.image_extensions = {'.jpg', '.jpeg', '.png', '.bmp', '.tiff', '.webp'}
        
        # 输出文件名模板
        self.output_json_template = "ocr_results_{timestamp}.json"
        self.output_csv_template = "ocr_results_{timestamp}.csv"
        self.output_summary_template = "ocr_summary_{timestamp}.txt"
    
    def get_data_path(self) -> Path:
        """获取数据目录路径"""
        return Path(self.data_dir)
    
    def get_output_path(self) -> Path:
        """获取输出目录路径"""
        output_path = Path(self.output_dir)
        output_path.mkdir(exist_ok=True)
        return output_path
    
    def get_output_filename(self, template: str) -> str:
        """生成带时间戳的输出文件名"""
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        return template.format(timestamp=timestamp)


class UniversalOCRProcessor:
    """通用OCR处理器"""
    
    def __init__(self, path_config: PathConfig = None):
        """
        初始化OCR处理器
        
        Args:
            path_config: 路径配置对象
        """
        self.path_config = path_config or PathConfig()
        self.ocr = None
        self._init_ocr()
    
    def _init_ocr(self):
        """初始化PaddleOCR"""
        print("正在初始化PaddleOCR...")
        use_gpu = False  # 默认值

        try:
            # 检查是否支持GPU和配置
            import paddle
            from config import config

            if config.USE_GPU and paddle.is_compiled_with_cuda() and paddle.device.cuda.device_count() > 0:
                use_gpu = True
                print(f"启用GPU加速 (设备数量: {paddle.device.cuda.device_count()})")
            else:
                if not config.USE_GPU:
                    print("配置中禁用了GPU，使用CPU模式")
                elif not paddle.is_compiled_with_cuda():
                    print("PaddlePaddle未编译CUDA支持，使用CPU模式")
                else:
                    print("未检测到GPU设备，使用CPU模式")

            # 初始化PaddleOCR (新版本)
            if use_gpu:
                self.ocr = PaddleOCR(
                    device='gpu',
                    lang='ch'
                )
            else:
                self.ocr = PaddleOCR(
                    device='cpu',
                    lang='ch'
                )
            print(f"PaddleOCR初始化成功！(GPU模式: {use_gpu})")

        except Exception as e:
            print(f"PaddleOCR初始化失败: {e}")
            # 如果GPU初始化失败，尝试CPU模式
            if use_gpu:
                print("GPU初始化失败，尝试CPU模式...")
                try:
                    self.ocr = PaddleOCR(device='cpu', lang='ch')
                    print("PaddleOCR CPU模式初始化成功！")
                except Exception as e2:
                    print(f"CPU模式初始化也失败: {e2}")
                    raise e2
            else:
                raise
    
    def extract_text_from_image(self, image_path: Union[str, Path]) -> List[Dict]:
        """
        从图片中提取文本
        
        Args:
            image_path: 图片路径
            
        Returns:
            文本信息列表，每个元素包含文本、置信度、坐标等信息
        """
        try:
            image_path = Path(image_path)
            print(f"  正在OCR识别: {image_path.name}")
            
            if not image_path.exists():
                print(f"  文件不存在: {image_path}")
                return []
            
            # 读取图片
            with open(image_path, 'rb') as f:
                image_data = f.read()
            
            nparr = np.frombuffer(image_data, np.uint8)
            img = cv2.imdecode(nparr, cv2.IMREAD_COLOR)

            if img is None:
                print("  无法解码图片")
                return []

            # 应用图片裁剪（如果启用）
            img = self._crop_image_if_enabled(img)
            
            print(f"  图片尺寸: {img.shape}")
            
            # OCR识别
            result = self.ocr.ocr(img)
            
            if not result or not result[0]:
                print("  OCR返回空结果")
                return []
            
            # 处理OCR结果
            texts = []
            ocr_result = result[0]
            
            # 处理不同格式的OCR结果
            if hasattr(ocr_result, 'rec_texts') and hasattr(ocr_result, 'rec_scores'):
                # 新版本OCRResult格式
                for i, (text, score, poly) in enumerate(zip(
                    ocr_result.rec_texts, 
                    ocr_result.rec_scores, 
                    ocr_result.rec_polys
                )):
                    texts.append({
                        'text': text,
                        'confidence': float(score),
                        'bbox': poly.tolist() if hasattr(poly, 'tolist') else poly,
                        'index': i
                    })
                    print(f"    文本{i+1}: '{text}' (置信度: {score:.3f})")
            
            elif isinstance(ocr_result, dict):
                # 字典格式
                if 'rec_texts' in ocr_result:
                    rec_texts = ocr_result['rec_texts']
                    rec_scores = ocr_result.get('rec_scores', [1.0] * len(rec_texts))
                    rec_polys = ocr_result.get('rec_polys', [None] * len(rec_texts))
                    
                    for i, (text, score, poly) in enumerate(zip(rec_texts, rec_scores, rec_polys)):
                        texts.append({
                            'text': text,
                            'confidence': float(score),
                            'bbox': poly.tolist() if poly is not None and hasattr(poly, 'tolist') else poly,
                            'index': i
                        })
                        print(f"    文本{i+1}: '{text}' (置信度: {score:.3f})")
            
            elif isinstance(ocr_result, list):
                # 传统列表格式
                for i, item in enumerate(ocr_result):
                    if len(item) == 2:
                        bbox, text_info = item
                        if isinstance(text_info, tuple) and len(text_info) == 2:
                            text, confidence = text_info
                            texts.append({
                                'text': text,
                                'confidence': float(confidence),
                                'bbox': bbox,
                                'index': i
                            })
                            print(f"    文本{i+1}: '{text}' (置信度: {confidence:.3f})")
            
            print(f"  共识别到 {len(texts)} 个文本区域")
            return texts
            
        except Exception as e:
            print(f"  OCR识别失败 {image_path}: {e}")
            return []
    
    def analyze_text_content(self, texts: List[Dict]) -> Dict:
        """
        分析文本内容，提取有用信息
        
        Args:
            texts: 文本信息列表
            
        Returns:
            分析结果字典
        """
        analysis = {
            'license_plates': [],
            'vehicle_models': [],
            'order_counts': [],
            'colors': [],
            'phone_numbers': [],
            'other_info': []
        }
        
        # 车牌号码正则表达式
        license_patterns = [
            r'[京津沪渝冀豫云辽黑湘皖鲁新苏浙赣鄂桂甘晋蒙陕吉闽贵粤青藏川宁琼使领][A-Z][A-Z0-9]{5}',
            r'[京津沪渝冀豫云辽黑湘皖鲁新苏浙赣鄂桂甘晋蒙陕吉闽贵粤青藏川宁琼使领][A-Z][A-Z0-9]{4}[A-Z0-9挂学警港澳]',
        ]
        
        # 颜色关键词
        color_keywords = ['蓝', '黄', '白', '黑', '绿', '红', '灰', '银']
        
        # 车型关键词 - 从配置文件读取
        from config import config
        vehicle_keywords = config.VEHICLE_KEYWORDS

        # 首先尝试从所有文本中找到完整的车型名称
        all_text = ' '.join([t['text'] for t in texts])
        complete_vehicle = self._find_complete_vehicle_model(all_text)
        if complete_vehicle:
            analysis['vehicle_models'].append({
                'model': complete_vehicle,
                'brand': self._extract_brand_from_model(complete_vehicle),
                'full_text': all_text,
                'confidence': 1.0
            })
            # 找到完整车型后仍继续处理，以防有其他车型
        
        # 接单次数模式
        order_patterns = [
            r'(\d+)\s*次',
            r'(\d+)\s*单',
            r'接单\s*(\d+)',
            r'(\d+)\s*接单'
        ]
        
        # 手机号码模式
        phone_pattern = r'1[3-9]\d{9}'
        
        for text_info in texts:
            text = text_info['text']
            confidence = text_info['confidence']
            
            if confidence < 0.5:  # 跳过置信度过低的文本
                continue
            
            # 查找车牌号码
            for pattern in license_patterns:
                matches = re.findall(pattern, text)
                for match in matches:
                    analysis['license_plates'].append({
                        'plate': match,
                        'full_text': text,
                        'confidence': confidence
                    })
            
            # 查找颜色
            for color in color_keywords:
                if color in text:
                    analysis['colors'].append({
                        'color': color,
                        'full_text': text,
                        'confidence': confidence
                    })
            
            # 查找车型 - 提取完整车型名称
            vehicle_found = False
            for vehicle in vehicle_keywords:
                if vehicle in text and not vehicle_found:
                    # 尝试提取完整的车型名称
                    full_model = self._extract_full_vehicle_model(text, vehicle)
                    # 只有当提取到的车型不等于品牌名时才添加（说明提取到了完整车型）
                    if full_model and full_model != vehicle:
                        analysis['vehicle_models'].append({
                            'model': full_model,
                            'brand': vehicle,
                            'full_text': text,
                            'confidence': confidence
                        })
                        vehicle_found = True  # 找到一个车型后就停止查找
            
            # 查找接单次数
            for pattern in order_patterns:
                matches = re.findall(pattern, text)
                for match in matches:
                    try:
                        count = int(match)
                        analysis['order_counts'].append({
                            'count': count,
                            'full_text': text,
                            'confidence': confidence
                        })
                    except ValueError:
                        continue
            
            # 查找手机号码
            phone_matches = re.findall(phone_pattern, text)
            for phone in phone_matches:
                analysis['phone_numbers'].append({
                    'phone': phone,
                    'full_text': text,
                    'confidence': confidence
                })
            
            # 其他信息
            analysis['other_info'].append({
                'text': text,
                'confidence': confidence
            })
        
        return analysis

    def _extract_full_vehicle_model(self, text: str, brand: str) -> str:
        """
        从文本中提取完整的车型名称

        Args:
            text: 包含车型信息的文本
            brand: 品牌关键词

        Returns:
            完整的车型名称
        """
        import re

        # 清理文本，移除不需要的后缀
        cleaned_text = self._clean_vehicle_text(text)

        # 特殊处理：如果文本中包含完整的品牌+车型组合，直接提取
        special_patterns = [
            # 哪吒汽车哪吒V, 小鹏汽车小鹏P7, 零跑汽车零跑C11 等
            rf'({re.escape(brand)}汽车{re.escape(brand)}[A-Za-z0-9\u4e00-\u9fa5]+)',
            # 深蓝汽车 深蓝SL03 等（带空格）
            rf'({re.escape(brand)}汽车\s+{re.escape(brand)}[A-Za-z0-9\u4e00-\u9fa5]+)',
            # 比亚迪宋PLUS, 广汽埃安AIONY 等
            rf'({re.escape(brand)}[汽车\s]*[A-Za-z0-9\u4e00-\u9fa5]+(?:\s*[A-Za-z0-9]+)*)',
        ]

        for pattern in special_patterns:
            matches = re.findall(pattern, cleaned_text, re.IGNORECASE)
            if matches:
                # 取最长的匹配结果
                full_model = max(matches, key=len).strip()
                if full_model and len(full_model) > len(brand):
                    # 清理结果，移除多余的符号
                    cleaned_model = self._clean_extracted_model(full_model)
                    if cleaned_model and cleaned_model != brand:
                        return cleaned_model

        # 常见的车型模式
        patterns = [
            # 品牌 + 车型名称 (如: 比亚迪宋PLUS)
            rf'{re.escape(brand)}\s*([A-Za-z0-9\u4e00-\u9fa5]+(?:\s*[A-Za-z0-9]+)*)',
            # 品牌汽车 + 车型 (如: 小鹏汽车P7)
            rf'{re.escape(brand)}汽车\s*([A-Za-z0-9\u4e00-\u9fa5]+(?:\s*[A-Za-z0-9]+)*)',
            # 品牌名称 + 空格 + 车型 (如: 广汽埃安 AION S)
            rf'{re.escape(brand)}\s+([A-Za-z0-9\u4e00-\u9fa5]+(?:\s*[A-Za-z0-9]+)*)',
        ]

        for pattern in patterns:
            matches = re.findall(pattern, cleaned_text, re.IGNORECASE)
            if matches:
                # 取最长的匹配结果
                model_part = max(matches, key=len).strip()
                if model_part and len(model_part) > 1:
                    # 清理结果
                    cleaned_model = self._clean_extracted_model(f"{brand} {model_part}")
                    if cleaned_model and cleaned_model != brand:
                        return cleaned_model

        # 如果没有找到完整车型，尝试查找常见车型后缀
        common_suffixes = [
            'PLUS', 'Pro', 'Max', 'Ultra', 'EV', 'DM', 'DMi', 'PHEV', 'EQS', 'EQC',
            'L', 'S', 'X', 'Y', 'V', 'U', 'Q5', 'Q7', 'A4L', 'A6L', 'C200L',
            '新能源', '混动', '纯电', '插混', 'C10', 'C11', 'P7', 'P5', 'SL03'
        ]

        # 在文本中查找品牌附近的车型信息
        brand_pos = cleaned_text.find(brand)
        if brand_pos != -1:
            # 提取品牌前后的文本
            start = max(0, brand_pos - 10)
            end = min(len(cleaned_text), brand_pos + len(brand) + 20)
            context = cleaned_text[start:end]

            # 查找车型后缀
            for suffix in common_suffixes:
                if suffix in context:
                    return f"{brand} {suffix}"

        # 如果都没找到，返回品牌名
        return brand

    def _clean_vehicle_text(self, text: str) -> str:
        """
        清理车型文本，移除不需要的内容
        """
        import re

        # 移除常见的不需要的后缀和无效内容
        unwanted_patterns = [
            r'丨查看图片.*',
            r'｜查看图片.*',
            r'查看图片.*',
            r'丨.*',
            r'｜.*',
            r'·.*',
            r'接单\d+次.*',
            r'尾号\d+.*',
            r'\d+次.*',
            r'查看.*',
            r'图片.*',
            r'>.*',  # 移除 > 及其后面的内容
            r'绿丨.*',  # 移除"绿丨"及其后面的内容
            r'蓝丨.*',  # 移除"蓝丨"及其后面的内容
            r'黄丨.*',  # 移除"黄丨"及其后面的内容
            r'白丨.*',  # 移除"白丨"及其后面的内容
        ]

        cleaned = text
        for pattern in unwanted_patterns:
            cleaned = re.sub(pattern, '', cleaned, flags=re.IGNORECASE)

        # 移除多余的空格和特殊字符
        cleaned = re.sub(r'\s+', ' ', cleaned)  # 多个空格变成一个
        cleaned = re.sub(r'[丨｜·>]+', '', cleaned)  # 移除这些特殊字符

        return cleaned.strip()

    def _clean_extracted_model(self, model: str) -> str:
        """
        清理提取的车型名称
        """
        import re

        # 移除特殊字符和不需要的内容
        cleaned = re.sub(r'[^\w\s\u4e00-\u9fa5]', '', model)
        cleaned = re.sub(r'\s+', ' ', cleaned)  # 合并多个空格

        # 移除地名和无效内容（这些不是车型）
        invalid_content = [
            '长安镇', '广州花', '花都', '万邦', '零售中心', '西南侧', '东北侧',
            '南门', '北门', '东门', '西门', '附近', '店', '中心', '大厦',
            '理想汽车广州花', '查看图片', '丨', '｜', '·'
        ]

        for invalid_item in invalid_content:
            if invalid_item in cleaned:
                # 如果包含无效内容，尝试从原文本中找到真正的车型
                real_model = self._find_real_vehicle_model(cleaned)
                return real_model  # 可能返回None

        return cleaned.strip()

    def _find_real_vehicle_model(self, text: str) -> str:
        """
        从包含地名的文本中找到真正的车型
        """
        # 常见的完整车型名称模式
        vehicle_patterns = [
            r'(小鹏汽车小鹏[A-Za-z0-9]+)',
            r'(特斯拉Model[A-Za-z0-9]+)',
            r'(比亚迪[A-Za-z0-9\u4e00-\u9fa5]+)',
            r'(广汽埃安[A-Za-z0-9\u4e00-\u9fa5]+)',
            r'(蔚来[A-Za-z0-9\u4e00-\u9fa5]+)',
            r'(理想[A-Za-z0-9\u4e00-\u9fa5]+)',
            r'(哪吒汽车哪吒[A-Za-z0-9]+)',
            r'(零跑汽车零跑[A-Za-z0-9]+)',
            r'(深蓝汽车深蓝[A-Za-z0-9]+)',
        ]

        import re
        for pattern in vehicle_patterns:
            match = re.search(pattern, text, re.IGNORECASE)
            if match:
                return match.group(1)

        # 如果没找到，返回None表示这不是有效的车型
        return None

    def _find_complete_vehicle_model(self, text: str) -> str:
        """
        从完整文本中找到车型名称
        """
        import re

        # 完整车型名称模式（按优先级排序）
        complete_patterns = [
            r'(小鹏汽车小鹏[A-Za-z0-9]+)',
            r'(哪吒汽车哪吒[A-Za-z0-9]+)',
            r'(零跑汽车零跑[A-Za-z0-9]+)',
            r'(深蓝汽车深蓝[A-Za-z0-9]+)',
            r'(特斯拉Model[A-Za-z0-9]+)',
            r'(比亚迪[A-Za-z0-9\u4e00-\u9fa5]+)',
            r'(广汽埃安[A-Za-z0-9\u4e00-\u9fa5]+)',
            r'(蔚来[A-Za-z0-9\u4e00-\u9fa5]+)',
            r'(哪吒汽车\s+哪吒[A-Za-z0-9]+)',  # 带空格的哪吒
            r'(问界[A-Za-z0-9\u4e00-\u9fa5]+)',
            r'(极氪[A-Za-z0-9\u4e00-\u9fa5]+)',
        ]

        # 收集所有匹配的车型
        all_matches = []
        for pattern in complete_patterns:
            matches = re.findall(pattern, text, re.IGNORECASE)
            for vehicle_model in matches:
                # 确保这不是地名
                if not any(place in vehicle_model for place in ['长安镇', '广州花', '花都', '万邦', '零售中心', '西南侧', '东北侧']):
                    all_matches.append(vehicle_model)

        if all_matches:
            # 优先选择最长的匹配（通常更完整）
            return max(all_matches, key=len)

        return None

    def _extract_brand_from_model(self, model: str) -> str:
        """
        从车型名称中提取品牌
        """
        if '汽车' in model:
            return model.split('汽车')[0]

        # 常见品牌
        brands = ['小鹏', '哪吒', '零跑', '深蓝', '特斯拉', '比亚迪', '广汽', '蔚来', '理想', '问界', '极氪']
        for brand in brands:
            if model.startswith(brand):
                return brand

        return model.split()[0] if ' ' in model else model

    def _extract_fallback_vehicle_model(self, ocr_texts: List[Dict]) -> str:
        """
        当主要车型识别失败时，尝试从所有文本中提取车型信息
        """
        from config import config

        # 合并所有文本
        all_text = ' '.join([t['text'] for t in ocr_texts if t['confidence'] > 0.3])

        # 清理文本
        cleaned_text = self._clean_vehicle_text(all_text)

        # 尝试找到完整车型
        complete_vehicle = self._find_complete_vehicle_model(cleaned_text)
        if complete_vehicle:
            return complete_vehicle

        # 尝试从品牌关键词中找到车型
        vehicle_keywords = config.VEHICLE_KEYWORDS
        for brand in vehicle_keywords:
            if brand in cleaned_text:
                # 尝试提取完整车型
                full_model = self._extract_full_vehicle_model(cleaned_text, brand)
                if full_model and full_model != brand:
                    return full_model

        # 如果还是没找到，尝试查找常见车型模式
        import re
        common_vehicle_patterns = [
            r'([A-Z]+\d+[A-Z]*)',  # 如 M5, P7, C11 等
            r'(Model[A-Z])',       # 如 ModelY, ModelS 等
            r'([A-Z]{2,}\s*[A-Z0-9]+)',  # 如 AION S 等
        ]

        for pattern in common_vehicle_patterns:
            matches = re.findall(pattern, cleaned_text)
            if matches:
                # 返回第一个匹配的车型
                return matches[0]

        # 最后尝试：如果文本中包含任何品牌名，就返回该品牌
        for brand in vehicle_keywords:
            if brand in cleaned_text:
                return brand

        return ''  # 如果什么都没找到，返回空字符串

    def _crop_image_if_enabled(self, image: np.ndarray) -> np.ndarray:
        """
        根据配置裁剪图片
        """
        from config import config

        if not config.ENABLE_IMAGE_CROP:
            return image

        height = image.shape[0]

        if config.CROP_MODE == 'bottom':
            # 保留下半部分
            start_y = int(height * (1 - config.CROP_RATIO))
            cropped_image = image[start_y:, :]
        elif config.CROP_MODE == 'top':
            # 保留上半部分
            end_y = int(height * config.CROP_RATIO)
            cropped_image = image[:end_y, :]
        else:
            # 无效模式，返回原图
            return image

        return cropped_image

    def process_single_image(self, image_path: Union[str, Path]) -> Dict:
        """
        处理单张图片

        Args:
            image_path: 图片路径

        Returns:
            处理结果字典
        """
        image_path = Path(image_path)

        # 解析路径信息
        path_parts = image_path.parts
        path_info = {
            'full_path': str(image_path),
            'filename': image_path.name,
            'city': '',
            'category': '',
            'person': '',
            'platform': ''
        }

        # 从路径中提取信息 (data/城市/类别/人员/平台/文件名)
        # 找到data目录的位置
        data_index = -1
        for i, part in enumerate(path_parts):
            if part == 'data':
                data_index = i
                break

        if data_index != -1:
            # 基于data目录的位置来解析路径
            if len(path_parts) > data_index + 1:
                path_info['city'] = path_parts[data_index + 1]
            if len(path_parts) > data_index + 2:
                path_info['category'] = path_parts[data_index + 2]
            if len(path_parts) > data_index + 3:
                path_info['person'] = path_parts[data_index + 3]
            if len(path_parts) > data_index + 4:
                path_info['platform'] = path_parts[data_index + 4]

        # OCR识别
        texts = self.extract_text_from_image(image_path)

        # 分析文本内容
        analysis = self.analyze_text_content(texts)

        # 组装结果
        result = {
            'path_info': path_info,
            'ocr_texts': texts,
            'analysis': analysis,
            'summary': {
                'total_texts': len(texts),
                'license_plates_found': len(analysis['license_plates']),
                'vehicle_models_found': len(analysis['vehicle_models']),
                'order_counts_found': len(analysis['order_counts']),
                'colors_found': len(analysis['colors']),
                'phone_numbers_found': len(analysis['phone_numbers'])
            }
        }

        return result

    def process_directory(self, data_dir: str = None) -> List[Dict]:
        """
        处理目录下的所有图片

        Args:
            data_dir: 数据目录路径，如果为None则使用配置中的路径

        Returns:
            所有图片的处理结果列表
        """
        if data_dir is None:
            data_path = self.path_config.get_data_path()
        else:
            data_path = Path(data_dir)

        if not data_path.exists():
            print(f"数据目录不存在: {data_path}")
            return []

        print(f"开始处理目录: {data_path}")

        results = []
        image_count = 0

        # 递归查找所有图片文件
        for image_file in data_path.rglob('*'):
            if image_file.suffix.lower() in self.path_config.image_extensions:
                image_count += 1
                print(f"\n处理第 {image_count} 张图片: {image_file.relative_to(data_path)}")

                result = self.process_single_image(image_file)
                results.append(result)

        print(f"\n处理完成！共处理 {len(results)} 张图片")
        return results

    def save_results_json(self, results: List[Dict], filename: str = None) -> str:
        """
        保存结果到JSON文件

        Args:
            results: 处理结果列表
            filename: 输出文件名，如果为None则自动生成

        Returns:
            保存的文件路径
        """
        if filename is None:
            filename = self.path_config.get_output_filename(
                self.path_config.output_json_template
            )

        output_path = self.path_config.get_output_path() / filename

        with open(output_path, 'w', encoding='utf-8') as f:
            json.dump(results, f, ensure_ascii=False, indent=2)

        print(f"JSON结果已保存到: {output_path}")
        return str(output_path)

    def save_results_csv(self, results: List[Dict], filename: str = None) -> str:
        """
        保存结果到CSV文件

        Args:
            results: 处理结果列表
            filename: 输出文件名，如果为None则自动生成

        Returns:
            保存的文件路径
        """
        if filename is None:
            filename = self.path_config.get_output_filename(
                self.path_config.output_csv_template
            )

        output_path = self.path_config.get_output_path() / filename

        with open(output_path, 'w', newline='', encoding='utf-8-sig') as f:
            writer = csv.writer(f)

            # 写入表头
            headers = [
                '人员', '平台', '车牌号码', '车牌颜色',
                '车型', '接单次数', '所有识别文本'
            ]
            writer.writerow(headers)

            # 写入数据
            for result in results:
                path_info = result['path_info']
                analysis = result['analysis']

                # 提取主要信息
                license_plate = analysis['license_plates'][0]['plate'] if analysis['license_plates'] else ''
                color = analysis['colors'][0]['color'] if analysis['colors'] else ''
                vehicle_model = analysis['vehicle_models'][0]['model'] if analysis['vehicle_models'] else ''
                order_count = analysis['order_counts'][0]['count'] if analysis['order_counts'] else ''

                # 如果车型为空，尝试从所有文本中提取车型信息
                if not vehicle_model:
                    vehicle_model = self._extract_fallback_vehicle_model(result['ocr_texts'])

                # 所有识别文本
                all_texts = []
                for text_info in result['ocr_texts']:
                    all_texts.append(f"{text_info['text']}({text_info['confidence']:.2f})")
                all_text_str = '; '.join(all_texts)

                row = [
                    path_info['person'],
                    path_info['platform'],
                    license_plate,
                    color,
                    vehicle_model,
                    order_count,
                    all_text_str
                ]
                writer.writerow(row)

        print(f"CSV结果已保存到: {output_path}")
        return str(output_path)

    def save_summary_report(self, results: List[Dict], filename: str = None) -> str:
        """
        保存汇总报告

        Args:
            results: 处理结果列表
            filename: 输出文件名，如果为None则自动生成

        Returns:
            保存的文件路径
        """
        if filename is None:
            filename = self.path_config.get_output_filename(
                self.path_config.output_summary_template
            )

        output_path = self.path_config.get_output_path() / filename

        # 统计信息
        total_images = len(results)
        total_texts = sum(r['summary']['total_texts'] for r in results)
        license_plates_found = sum(r['summary']['license_plates_found'] for r in results)
        vehicle_models_found = sum(r['summary']['vehicle_models_found'] for r in results)
        order_counts_found = sum(r['summary']['order_counts_found'] for r in results)
        phone_numbers_found = sum(r['summary']['phone_numbers_found'] for r in results)

        # 按城市统计
        city_stats = {}
        for result in results:
            city = result['path_info']['city'] or '未知'
            if city not in city_stats:
                city_stats[city] = 0
            city_stats[city] += 1

        # 按平台统计
        platform_stats = {}
        for result in results:
            platform = result['path_info']['platform'] or '未知'
            if platform not in platform_stats:
                platform_stats[platform] = 0
            platform_stats[platform] += 1

        with open(output_path, 'w', encoding='utf-8') as f:
            f.write("OCR处理结果汇总报告\n")
            f.write("=" * 50 + "\n\n")
            f.write(f"处理时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n")
            f.write(f"数据目录: {self.path_config.data_dir}\n\n")

            f.write("总体统计:\n")
            f.write(f"  总图片数: {total_images}\n")
            f.write(f"  总识别文本数: {total_texts}\n")
            f.write(f"  识别到车牌: {license_plates_found}\n")
            f.write(f"  识别到车型: {vehicle_models_found}\n")
            f.write(f"  识别到接单次数: {order_counts_found}\n")
            f.write(f"  识别到手机号码: {phone_numbers_found}\n\n")

            f.write("按城市统计:\n")
            for city, count in sorted(city_stats.items()):
                f.write(f"  {city}: {count} 张\n")
            f.write("\n")

            f.write("按平台统计:\n")
            for platform, count in sorted(platform_stats.items()):
                f.write(f"  {platform}: {count} 张\n")
            f.write("\n")

            f.write("详细结果:\n")
            f.write("-" * 50 + "\n")
            for i, result in enumerate(results, 1):
                path_info = result['path_info']
                analysis = result['analysis']

                f.write(f"{i}. {path_info['filename']}\n")
                f.write(f"   路径: {path_info['full_path']}\n")
                f.write(f"   城市: {path_info['city']}\n")
                f.write(f"   人员: {path_info['person']}\n")
                f.write(f"   平台: {path_info['platform']}\n")

                if analysis['license_plates']:
                    f.write(f"   车牌: {analysis['license_plates'][0]['plate']}\n")
                if analysis['colors']:
                    f.write(f"   颜色: {analysis['colors'][0]['color']}\n")
                if analysis['vehicle_models']:
                    f.write(f"   车型: {analysis['vehicle_models'][0]['model']}\n")
                if analysis['order_counts']:
                    f.write(f"   接单次数: {analysis['order_counts'][0]['count']}\n")

                f.write(f"   识别文本数: {result['summary']['total_texts']}\n")
                f.write("\n")

        print(f"汇总报告已保存到: {output_path}")
        return str(output_path)


def main():
    """主函数"""
    print("通用OCR处理器启动...")
    print("=" * 50)

    try:
        # 创建路径配置
        # 可以在这里修改路径配置
        path_config = PathConfig(
            python_path=r"D:\ProgramData\miniconda3\envs\basePyEnv\python.exe",
            data_dir="data",
            output_dir="output"
        )

        print(f"Python路径: {path_config.python_path}")
        print(f"数据目录: {path_config.data_dir}")
        print(f"输出目录: {path_config.output_dir}")
        print()

        # 初始化OCR处理器
        processor = UniversalOCRProcessor(path_config)

        # 处理所有图片
        results = processor.process_directory()

        if not results:
            print("没有找到任何图片文件或处理失败")
            return

        # 保存结果
        print("\n保存处理结果...")
        json_file = processor.save_results_json(results)
        csv_file = processor.save_results_csv(results)
        summary_file = processor.save_summary_report(results)

        print("\n处理完成！")
        print(f"JSON结果: {json_file}")
        print(f"CSV结果: {csv_file}")
        print(f"汇总报告: {summary_file}")

        # 显示简要统计
        total_images = len(results)
        license_count = sum(1 for r in results if r['analysis']['license_plates'])
        vehicle_count = sum(1 for r in results if r['analysis']['vehicle_models'])
        order_count = sum(1 for r in results if r['analysis']['order_counts'])

        print(f"\n统计信息:")
        print(f"  总图片数: {total_images}")
        print(f"  识别到车牌: {license_count}")
        print(f"  识别到车型: {vehicle_count}")
        print(f"  识别到接单次数: {order_count}")

    except Exception as e:
        print(f"处理过程中出现错误: {e}")
        import traceback
        traceback.print_exc()


def demo_custom_paths():
    """演示如何使用自定义路径配置"""
    print("自定义路径配置演示...")

    # 创建自定义路径配置
    custom_config = PathConfig(
        python_path=r"C:\Python39\python.exe",  # 自定义Python路径
        data_dir="my_data",                      # 自定义数据目录
        output_dir="my_output"                   # 自定义输出目录
    )

    # 使用自定义配置创建处理器
    processor = UniversalOCRProcessor(custom_config)

    # 处理指定目录
    results = processor.process_directory("my_data")

    # 保存到自定义位置
    processor.save_results_csv(results, "custom_results.csv")


def demo_single_image():
    """演示如何处理单张图片"""
    print("单张图片处理演示...")

    processor = UniversalOCRProcessor()

    # 处理单张图片
    image_path = "data/广州/双边渗透率/唐玉凤/哈啰/Screenshot_2025-08-15-20-53-16-210_com.jingyao.easybike.jpg"

    if Path(image_path).exists():
        result = processor.process_single_image(image_path)

        print(f"图片: {result['path_info']['filename']}")
        print(f"识别文本数: {result['summary']['total_texts']}")

        if result['analysis']['license_plates']:
            print(f"车牌: {result['analysis']['license_plates'][0]['plate']}")

        if result['analysis']['vehicle_models']:
            print(f"车型: {result['analysis']['vehicle_models'][0]['model']}")
    else:
        print(f"图片文件不存在: {image_path}")


if __name__ == "__main__":
    # 运行主程序
    main()

    # 如果需要演示其他功能，可以取消注释下面的行
    # print("\n" + "="*50)
    # demo_single_image()

    # print("\n" + "="*50)
    # demo_custom_paths()
