#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
OCR处理器启动脚本
简化的启动接口，支持命令行参数
"""

import sys
import argparse
from pathlib import Path

# 导入主程序
from main_ocr_processor import main
from config import config


def parse_arguments():
    """解析命令行参数"""
    parser = argparse.ArgumentParser(
        description="通用OCR处理器 - 批量处理图片提取文本信息",
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
使用示例:
  python run_ocr.py                          # 使用默认配置处理data目录
  python run_ocr.py --data-dir my_images     # 处理指定目录
  python run_ocr.py --output-dir results     # 指定输出目录
  python run_ocr.py --python-path "C:\\Python39\\python.exe"  # 指定Python路径
  python run_ocr.py --config                 # 显示当前配置
        """
    )
    
    parser.add_argument(
        '--data-dir', 
        type=str, 
        default=None,
        help=f'数据目录路径 (默认: {config.DATA_DIR})'
    )
    
    parser.add_argument(
        '--output-dir', 
        type=str, 
        default=None,
        help=f'输出目录路径 (默认: {config.OUTPUT_DIR})'
    )
    
    parser.add_argument(
        '--python-path', 
        type=str, 
        default=None,
        help=f'Python解释器路径 (默认: {config.PYTHON_PATH})'
    )
    
    parser.add_argument(
        '--config', 
        action='store_true',
        help='显示当前配置信息'
    )
    
    parser.add_argument(
        '--no-gpu', 
        action='store_true',
        help='禁用GPU加速'
    )
    
    parser.add_argument(
        '--min-confidence', 
        type=float, 
        default=None,
        help=f'最小置信度阈值 (默认: {config.MIN_CONFIDENCE})'
    )
    
    parser.add_argument(
        '--quiet', 
        action='store_true',
        help='静默模式，减少输出信息'
    )
    
    return parser.parse_args()


def update_config_from_args(args):
    """根据命令行参数更新配置"""
    if args.data_dir:
        config.DATA_DIR = args.data_dir
        print(f"数据目录设置为: {config.DATA_DIR}")
    
    if args.output_dir:
        config.OUTPUT_DIR = args.output_dir
        print(f"输出目录设置为: {config.OUTPUT_DIR}")
    
    if args.python_path:
        config.PYTHON_PATH = args.python_path
        print(f"Python路径设置为: {config.PYTHON_PATH}")
    
    if args.no_gpu:
        config.USE_GPU = False
        print("GPU加速已禁用")
    
    if args.min_confidence is not None:
        config.MIN_CONFIDENCE = args.min_confidence
        print(f"最小置信度设置为: {config.MIN_CONFIDENCE}")
    
    if args.quiet:
        config.SHOW_PROGRESS = False
        config.SHOW_OCR_LOG = False
        print("静默模式已启用")


def check_data_directory():
    """检查数据目录是否存在"""
    data_path = Path(config.DATA_DIR)
    if not data_path.exists():
        print(f"错误: 数据目录不存在: {data_path}")
        print("请确保数据目录存在，或使用 --data-dir 参数指定正确的路径")
        return False
    
    # 检查是否有图片文件
    image_count = 0
    for file_path in data_path.rglob('*'):
        if file_path.suffix.lower() in config.SUPPORTED_IMAGE_EXTENSIONS:
            image_count += 1
            if image_count >= 1:  # 找到至少一个图片文件就够了
                break
    
    if image_count == 0:
        print(f"警告: 在数据目录中没有找到任何图片文件: {data_path}")
        print(f"支持的格式: {', '.join(config.SUPPORTED_IMAGE_EXTENSIONS)}")
        return False
    
    return True


def main_wrapper():
    """主函数包装器"""
    try:
        # 解析命令行参数
        args = parse_arguments()
        
        # 如果只是显示配置
        if args.config:
            print("当前配置信息:")
            print("=" * 50)
            config.print_config()
            return 0
        
        # 根据参数更新配置
        update_config_from_args(args)
        
        # 检查数据目录
        if not check_data_directory():
            return 1
        
        # 运行主程序
        return main()
        
    except KeyboardInterrupt:
        print("\n用户中断程序")
        return 1
    except Exception as e:
        print(f"程序启动失败: {e}")
        return 1


if __name__ == "__main__":
    exit_code = main_wrapper()
    sys.exit(exit_code)
