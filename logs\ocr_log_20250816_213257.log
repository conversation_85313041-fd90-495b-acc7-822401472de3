2025-08-16 21:32:57,720 - INFO - 日志文件: C:\Users\<USER>\Desktop\ee\py_ocr\logs\ocr_log_20250816_213257.log
2025-08-16 21:32:57,720 - INFO - 程序启动
2025-08-16 21:32:57,739 - INFO - 初始化OCR处理器...
2025-08-16 21:32:58,365 - ERROR - 程序执行出错: Unknown argument: use_gpu
Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\ee\py_ocr\main_ocr_processor.py", line 307, in main
    processor = UniversalOCRProcessor(path_config)
  File "C:\Users\<USER>\Desktop\ee\py_ocr\universal_ocr_processor.py", line 70, in __init__
    self._init_ocr()
    ~~~~~~~~~~~~~~^^
  File "C:\Users\<USER>\Desktop\ee\py_ocr\universal_ocr_processor.py", line 94, in _init_ocr
    self.ocr = PaddleOCR(
               ~~~~~~~~~^
        use_gpu=use_gpu,
        ^^^^^^^^^^^^^^^^
        lang='ch'
        ^^^^^^^^^
    )
    ^
  File "C:\Python313\Lib\site-packages\paddleocr\_pipelines\ocr.py", line 161, in __init__
    super().__init__(**base_params)
    ~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^
  File "C:\Python313\Lib\site-packages\paddleocr\_pipelines\base.py", line 62, in __init__
    self._common_args = parse_common_args(
                        ~~~~~~~~~~~~~~~~~^
        common_args, default_enable_hpi=_DEFAULT_ENABLE_HPI
        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    )
    ^
  File "C:\Python313\Lib\site-packages\paddleocr\_common_args.py", line 43, in parse_common_args
    raise ValueError(f"Unknown argument: {name}")
ValueError: Unknown argument: use_gpu
