# 车牌识别脚本

使用PaddleOCR识别图片中的车牌信息，包括车牌号码、颜色、车型和接单次数。

## 安装依赖

```bash
pip install -r requirements.txt
```

## 使用方法

1. 将需要识别的图片放在 `data` 目录下（支持多级子目录）
2. 运行脚本：

```bash
python ocr_license_plate.py
```

## 功能特性

- **车牌识别**: 识别中国大陆车牌号码
- **颜色识别**: 识别车牌颜色（蓝、黄、白、黑、绿、红）
- **车型识别**: 识别常见车型和品牌
- **接单次数**: 识别图片右侧的接单次数

## 输出结果

脚本会生成以下输出：

1. **控制台输出**: 显示识别进度和结果摘要
2. **JSON文件**: `ocr_results.json` 包含详细的识别结果

## 支持的图片格式

- JPG/JPEG
- PNG
- BMP
- TIFF
- WebP

## 注意事项

- 首次运行会下载PaddleOCR模型，需要网络连接
- 建议图片清晰度较高以获得更好的识别效果
- 车牌识别基于正则表达式匹配，支持标准格式车牌
