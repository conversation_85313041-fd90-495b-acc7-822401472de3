2025-08-16 21:32:08,507 - INFO - 日志文件: C:\Users\<USER>\Desktop\ee\py_ocr\logs\ocr_log_20250816_213208.log
2025-08-16 21:32:08,507 - INFO - 程序启动
2025-08-16 21:32:08,527 - INFO - 初始化OCR处理器...
2025-08-16 21:32:08,652 - ERROR - 程序执行出错: cannot access local variable 'use_gpu' where it is not associated with a value
Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\ee\py_ocr\universal_ocr_processor.py", line 77, in _init_ocr
    import paddle
  File "C:\Python313\Lib\site-packages\paddle\__init__.py", line 38, in <module>
    from .base import core  # noqa: F401
    ^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Python313\Lib\site-packages\paddle\base\__init__.py", line 38, in <module>
    from . import (  # noqa: F401
    ...<14 lines>...
    )
  File "C:\Python313\Lib\site-packages\paddle\base\backward.py", line 28, in <module>
    from . import core, framework, log_helper, unique_name
  File "C:\Python313\Lib\site-packages\paddle\base\core.py", line 610, in <module>
    __check_and_set_prim_all_enabled(print_flag=True)
    ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^
  File "C:\Python313\Lib\site-packages\paddle\base\core.py", line 592, in __check_and_set_prim_all_enabled
    from paddle.utils.environments import strtobool
  File "C:\Python313\Lib\site-packages\paddle\utils\__init__.py", line 16, in <module>
    from . import (  # noqa: F401
    ...<6 lines>...
    )
  File "C:\Python313\Lib\site-packages\paddle\utils\cpp_extension\__init__.py", line 15, in <module>
    from .cpp_extension import (
    ...<5 lines>...
    )
  File "C:\Python313\Lib\site-packages\paddle\utils\cpp_extension\cpp_extension.py", line 23, in <module>
    import setuptools
ModuleNotFoundError: No module named 'setuptools'

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\ee\py_ocr\main_ocr_processor.py", line 307, in main
    processor = UniversalOCRProcessor(path_config)
  File "C:\Users\<USER>\Desktop\ee\py_ocr\universal_ocr_processor.py", line 70, in __init__
    self._init_ocr()
    ~~~~~~~~~~~~~~^^
  File "C:\Users\<USER>\Desktop\ee\py_ocr\universal_ocr_processor.py", line 102, in _init_ocr
    if use_gpu:
       ^^^^^^^
UnboundLocalError: cannot access local variable 'use_gpu' where it is not associated with a value
