#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
主OCR处理程序
使用配置文件管理所有参数，便于修改和维护
"""

import os
import sys
import time
import logging
from pathlib import Path
from datetime import datetime
from typing import List, Dict, Optional

# 导入配置和处理器
from config import config
from universal_ocr_processor import UniversalOCRProcessor, PathConfig


def setup_logging() -> logging.Logger:
    """设置日志记录"""
    log_dir = config.get_log_path()
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    log_file = log_dir / f"ocr_log_{timestamp}.log"
    
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(levelname)s - %(message)s',
        handlers=[
            logging.FileHandler(log_file, encoding='utf-8'),
            logging.StreamHandler(sys.stdout)
        ]
    )
    
    logger = logging.getLogger(__name__)
    logger.info(f"日志文件: {log_file}")
    return logger


def create_path_config_from_settings() -> PathConfig:
    """从配置文件创建路径配置对象"""
    return PathConfig(
        python_path=config.PYTHON_PATH,
        data_dir=config.DATA_DIR,
        output_dir=config.OUTPUT_DIR
    )


def print_banner():
    """打印程序横幅"""
    banner = """
    ╔══════════════════════════════════════════════════════════════╗
    ║                     通用OCR处理器 v1.0                        ║
    ║                                                              ║
    ║  功能: 批量处理图片，提取文本信息                              ║
    ║  支持: 车牌识别、车型识别、接单次数、手机号码等                  ║
    ║  输出: JSON、CSV、汇总报告                                    ║
    ╚══════════════════════════════════════════════════════════════╝
    """
    print(banner)


def check_dependencies():
    """检查依赖项"""
    print("检查依赖项...")

    # 检查实际的模块名称
    required_modules = [
        ('paddleocr', 'paddleocr'),
        ('opencv-python', 'cv2'),
        ('numpy', 'numpy'),
        ('Pillow', 'PIL')
    ]

    missing_packages = []

    for package_name, module_name in required_modules:
        try:
            __import__(module_name)
            print(f"  ✓ {package_name}")
        except ImportError:
            print(f"  ✗ {package_name} (缺失)")
            missing_packages.append(package_name)

    if missing_packages:
        print(f"\n缺失的依赖包: {', '.join(missing_packages)}")
        print("请运行以下命令安装:")
        print(f"pip install {' '.join(missing_packages)}")
        return False

    print("所有依赖项检查通过！")
    return True


def scan_data_directory(data_path: Path) -> Dict:
    """扫描数据目录，统计文件信息"""
    print(f"扫描数据目录: {data_path}")
    
    if not data_path.exists():
        print(f"数据目录不存在: {data_path}")
        return {}
    
    stats = {
        'total_files': 0,
        'image_files': 0,
        'cities': set(),
        'platforms': set(),
        'persons': set(),
        'file_sizes': []
    }
    
    for file_path in data_path.rglob('*'):
        if file_path.is_file():
            stats['total_files'] += 1
            
            if file_path.suffix.lower() in config.SUPPORTED_IMAGE_EXTENSIONS:
                stats['image_files'] += 1
                stats['file_sizes'].append(file_path.stat().st_size)
                
                # 解析路径信息
                parts = file_path.relative_to(data_path).parts
                if len(parts) >= 1:
                    stats['cities'].add(parts[0])
                if len(parts) >= 3:
                    stats['persons'].add(parts[2])
                if len(parts) >= 4:
                    stats['platforms'].add(parts[3])
    
    # 计算文件大小统计
    if stats['file_sizes']:
        total_size = sum(stats['file_sizes'])
        avg_size = total_size / len(stats['file_sizes'])
        stats['total_size_mb'] = total_size / (1024 * 1024)
        stats['avg_size_kb'] = avg_size / 1024
    else:
        stats['total_size_mb'] = 0
        stats['avg_size_kb'] = 0
    
    print(f"  总文件数: {stats['total_files']}")
    print(f"  图片文件数: {stats['image_files']}")
    print(f"  总大小: {stats['total_size_mb']:.2f} MB")
    print(f"  平均大小: {stats['avg_size_kb']:.2f} KB")
    print(f"  城市数: {len(stats['cities'])}")
    print(f"  人员数: {len(stats['persons'])}")
    print(f"  平台数: {len(stats['platforms'])}")
    
    if stats['cities']:
        print(f"  城市列表: {', '.join(sorted(stats['cities']))}")
    if stats['platforms']:
        print(f"  平台列表: {', '.join(sorted(stats['platforms']))}")
    
    return stats


def process_with_progress(processor: UniversalOCRProcessor, logger: logging.Logger) -> List[Dict]:
    """带进度显示的处理函数"""
    data_path = config.get_data_path()
    
    # 获取所有图片文件
    image_files = []
    for file_path in data_path.rglob('*'):
        if file_path.suffix.lower() in config.SUPPORTED_IMAGE_EXTENSIONS:
            image_files.append(file_path)
    
    total_files = len(image_files)
    if total_files == 0:
        logger.warning("没有找到任何图片文件")
        return []
    
    logger.info(f"开始处理 {total_files} 张图片")
    
    results = []
    start_time = time.time()
    
    for i, image_file in enumerate(image_files, 1):
        try:
            if config.SHOW_PROGRESS:
                progress = (i / total_files) * 100
                print(f"\r处理进度: {i}/{total_files} ({progress:.1f}%) - {image_file.name}", end='', flush=True)
            
            result = processor.process_single_image(image_file)
            results.append(result)
            
            # 记录处理结果
            summary = result['summary']
            logger.info(f"处理完成: {image_file.name} - 文本数:{summary['total_texts']} "
                       f"车牌:{summary['license_plates_found']} 车型:{summary['vehicle_models_found']}")
            
        except Exception as e:
            logger.error(f"处理失败: {image_file.name} - {e}")
            if not config.CONTINUE_ON_ERROR:
                raise
    
    if config.SHOW_PROGRESS:
        print()  # 换行
    
    elapsed_time = time.time() - start_time
    logger.info(f"处理完成，耗时: {elapsed_time:.2f} 秒")
    logger.info(f"平均每张图片: {elapsed_time/total_files:.2f} 秒")
    
    return results


def save_all_results(processor: UniversalOCRProcessor, results: List[Dict], logger: logging.Logger):
    """保存结果"""
    logger.info("保存处理结果...")

    try:
        from config import config

        if config.GENERATE_CSV_ONLY:
            # 只保存CSV
            csv_file = processor.save_results_csv(results)
            logger.info(f"CSV结果已保存: {csv_file}")

            return {
                'csv_file': csv_file
            }
        else:
            # 保存所有格式（保持向后兼容）
            json_file = processor.save_results_json(results)
            logger.info(f"JSON结果已保存: {json_file}")

            csv_file = processor.save_results_csv(results)
            logger.info(f"CSV结果已保存: {csv_file}")

            summary_file = processor.save_summary_report(results)
            logger.info(f"汇总报告已保存: {summary_file}")

            return {
                'json_file': json_file,
                'csv_file': csv_file,
                'summary_file': summary_file
            }

    except Exception as e:
        logger.error(f"保存结果失败: {e}")
        raise


def print_final_statistics(results: List[Dict], logger: logging.Logger):
    """打印最终统计信息"""
    if not results:
        return
    
    total_images = len(results)
    total_texts = sum(r['summary']['total_texts'] for r in results)
    license_count = sum(r['summary']['license_plates_found'] for r in results)
    vehicle_count = sum(r['summary']['vehicle_models_found'] for r in results)
    order_count = sum(r['summary']['order_counts_found'] for r in results)
    phone_count = sum(r['summary']['phone_numbers_found'] for r in results)
    
    print("\n" + "="*60)
    print("最终统计结果")
    print("="*60)
    print(f"总图片数:        {total_images}")
    print(f"总识别文本数:    {total_texts}")
    print(f"识别到车牌:      {license_count} ({license_count/total_images*100:.1f}%)")
    print(f"识别到车型:      {vehicle_count} ({vehicle_count/total_images*100:.1f}%)")
    print(f"识别到接单次数:  {order_count} ({order_count/total_images*100:.1f}%)")
    print(f"识别到手机号码:  {phone_count} ({phone_count/total_images*100:.1f}%)")
    print("="*60)
    
    # 记录到日志
    logger.info(f"最终统计 - 图片:{total_images} 文本:{total_texts} "
               f"车牌:{license_count} 车型:{vehicle_count} 接单:{order_count} 手机:{phone_count}")


def main():
    """主函数"""
    try:
        # 打印横幅
        print_banner()
        
        # 设置日志
        logger = setup_logging()
        logger.info("程序启动")
        
        # 打印配置信息
        config.print_config()
        print()
        
        # 验证配置
        if not config.validate_config():
            logger.error("配置验证失败，程序退出")
            return 1
        
        # 检查依赖项
        if not check_dependencies():
            logger.error("依赖项检查失败，程序退出")
            return 1
        
        print()
        
        # 扫描数据目录
        data_stats = scan_data_directory(config.get_data_path())
        if data_stats.get('image_files', 0) == 0:
            logger.error("没有找到任何图片文件")
            return 1
        
        print()
        
        # 创建处理器
        logger.info("初始化OCR处理器...")
        path_config = create_path_config_from_settings()
        processor = UniversalOCRProcessor(path_config)
        logger.info("OCR处理器初始化完成")
        
        # 处理所有图片
        results = process_with_progress(processor, logger)
        
        if not results:
            logger.error("没有成功处理任何图片")
            return 1
        
        # 保存结果
        output_files = save_all_results(processor, results, logger)
        
        # 打印统计信息
        print_final_statistics(results, logger)
        
        # 打印输出文件信息
        print("\n输出文件:")
        for file_type, file_path in output_files.items():
            if file_type == 'csv_file':
                print(f"CSV文件: {file_path}")
            elif file_type == 'json_file':
                print(f"JSON文件: {file_path}")
            elif file_type == 'summary_file':
                print(f"汇总报告: {file_path}")
            else:
                print(f"{file_type}: {file_path}")
        
        logger.info("程序执行完成")
        return 0
        
    except KeyboardInterrupt:
        print("\n用户中断程序")
        logger.info("用户中断程序")
        return 1
        
    except Exception as e:
        print(f"\n程序执行出错: {e}")
        logger.error(f"程序执行出错: {e}", exc_info=True)
        return 1


if __name__ == "__main__":
    exit_code = main()
    sys.exit(exit_code)
